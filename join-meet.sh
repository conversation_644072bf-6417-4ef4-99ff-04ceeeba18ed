#!/bin/bash

# Required parameters:
# @raycast.schemaVersion 1
# @raycast.title Join Meeting
# @raycast.mode silent

# Optional parameters:
# @raycast.icon 📹
# @raycast.argument1 { "type": "text", "placeholder": "会议号", "optional": true }
# @raycast.packageName 会议工具
# @raycast.needsConfirmation false

# Documentation:
# @raycast.author raikyou
# @raycast.authorURL https://raycast.com/raikyou

meetingNumber=""
defaultMeetingNumber="9037231696"

# Function to check if clipboard contains a valid meeting number
check_clipboard_for_meeting() {
    local clipboardContent=$(pbpaste 2>/dev/null)

    # Check if clipboard content looks like a meeting number (digits only, reasonable length)
    if [[ -n "$clipboardContent" && "$clipboardContent" =~ ^[0-9]+$ && ${#clipboardContent} -ge 6 && ${#clipboardContent} -le 15 ]]; then
        echo "$clipboardContent"
    else
        echo ""
    fi
}

# Try to get meeting number from clipboard first
clipboardMeeting=$(check_clipboard_for_meeting)
if [[ -n "$clipboardMeeting" ]]; then
    meetingNumber="$clipboardMeeting"
fi

# If no selected text, check for command line argument
if [[ -z "$meetingNumber" ]]; then
    if [[ -n "$1" ]]; then
        meetingNumber="$1"
    else
        # Use default meeting number if no input provided
        meetingNumber="$defaultMeetingNumber"
    fi
fi

# Base64 encode the meeting number
encodedMeetingNumber=$(printf '%s' "$meetingNumber" | base64 | tr -d '\n')

# Build meeting URL
meetingURL="https://meet.xylink.com/?number=$encodedMeetingNumber"

# Open meeting URL in Chrome
open -a "Google Chrome" "$meetingURL"

# Show notification using osascript
osascript -e "display notification \"已打开会议: $meetingNumber\" with title \"会议加入成功\""

