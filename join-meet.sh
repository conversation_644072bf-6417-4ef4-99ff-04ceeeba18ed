#!/bin/bash

# Required parameters:
# @raycast.schemaVersion 1
# @raycast.title Join Meeting
# @raycast.mode silent

# Optional parameters:
# @raycast.icon 📹
# @raycast.argument1 { "type": "text", "placeholder": "会议号", "optional": true }
# @raycast.packageName 会议工具
# @raycast.needsConfirmation false

# Documentation:
# @raycast.author raikyou
# @raycast.authorURL https://raycast.com/raikyou

meetingNumber=""
defaultMeetingNumber="9037231696"

# Function to get selected text from clipboard
get_selected_text() {
    # Clear clipboard first
    pbcopy < /dev/null

    # Copy selected text to clipboard using AppleScript
    osascript -e 'tell application "System Events" to keystroke "c" using {command down}' 2>/dev/null

    # Wait a bit for the copy operation
    sleep 0.3

    # Get clipboard content
    selectedText=$(pbpaste 2>/dev/null)

    # Check if text is valid (not empty and doesn't contain spaces)
    if [[ -n "$selectedText" && "$selectedText" != *" "* ]]; then
        echo "$selectedText"
    else
        echo ""
    fi
}

# Try to get selected text first
selectedText=$(get_selected_text)
if [[ -n "$selectedText" ]]; then
    meetingNumber="$selectedText"
fi

# If no selected text, check for command line argument
if [[ -z "$meetingNumber" ]]; then
    if [[ -n "$1" ]]; then
        meetingNumber="$1"
    else
        # Use default meeting number if no input provided
        meetingNumber="$defaultMeetingNumber"
    fi
fi

# Base64 encode the meeting number
encodedMeetingNumber=$(printf '%s' "$meetingNumber" | base64 | tr -d '\n')

# Build meeting URL
meetingURL="https://meet.xylink.com/?number=$encodedMeetingNumber"

# Open meeting URL in Chrome
open -a "Google Chrome" "$meetingURL"

# Show notification using osascript
osascript -e "display notification \"已打开会议: $meetingNumber\" with title \"会议加入成功\""

