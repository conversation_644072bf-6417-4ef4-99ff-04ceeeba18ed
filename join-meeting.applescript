#!/usr/bin/osascript

# Required parameters:
# @raycast.schemaVersion 1
# @raycast.title Join Meeting◊
# @raycast.mode silent

# Optional parameters:
# @raycast.icon 📹
# @raycast.argument1 { "type": "text", "placeholder": "会议号", "optional": true }
# @raycast.packageName 会议工具
# @raycast.needsConfirmation false

# Documentation:
# @raycast.author raikyou
# @raycast.authorURL https://raycast.com/raikyou

on run argv
  set meetingNumber to ""
  set defaultMeetingNumber to "9037231696"
  
  # 首先尝试获取选中的文本
  try
    # 先清空剪贴板
    set the clipboard to ""
    
    tell application "System Events" to keystroke "c" using {command down}
    delay 0.3
    set selectedText to the clipboard
    
    if selectedText is not "" and selectedText does not contain " " then
      set meetingNumber to selectedText
    end if
  on error
    # 出错时不做处理，继续检查其他选项
  end try
  
  # 如果没有选中文本，检查是否有输入参数
  if meetingNumber is "" then
    if (count of argv) > 0 and (item 1 of argv) is not "" then
      set meeting<PERSON><PERSON>ber to item 1 of argv
    else
      # 既没有选中文本也没有输入参数，使用默认会议号
      set meetingNumber to defaultMeetingNumber
    end if
  end if
  
  # 对会议号进行 Base64 编码
  set encodedMeetingNumber to do shell script "printf '" & meetingNumber & "' | base64 | tr -d '\\n'"
  
  # 构建会议链接
  set meetingURL to "https://meet.xylink.com/?number=" & encodedMeetingNumber
  
  # 使用标准浏览器打开会议链接
  do shell script "open -a 'Google Chrome' " & quoted form of meetingURL
  
  display notification "已打开会议: " & meetingNumber with title "会议加入成功"
end run

